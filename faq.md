# 常见问题

本页面收集了用户在使用易华CCP过程中遇到的常见问题和解决方案。

## 安装和部署

### Q: 安装过程中提示"端口被占用"怎么办？

**A:** 这通常是因为8080端口已被其他程序占用。解决方法：

1. **查看端口占用情况**：
   ```bash
   # Linux/macOS
   netstat -tlnp | grep 8080
   
   # Windows
   netstat -ano | findstr 8080
   ```

2. **停止占用端口的程序**或**修改配置文件**：
   ```yaml
   # application.yml
   server:
     port: 8081  # 改为其他端口
   ```

### Q: 数据库连接失败，显示"Access denied"错误？

**A:** 这是数据库权限问题，请检查：

1. **用户名和密码是否正确**
2. **数据库用户是否有足够权限**：
   ```sql
   GRANT ALL PRIVILEGES ON yihua_ccp.* TO 'username'@'localhost';
   FLUSH PRIVILEGES;
   ```
3. **数据库服务是否正常运行**：
   ```bash
   systemctl status mysql
   ```

### Q: Docker部署时容器启动失败？

**A:** 常见原因和解决方法：

1. **检查Docker日志**：
   ```bash
   docker logs yihua-ccp
   ```

2. **确保环境变量正确设置**：
   ```bash
   docker run -e DB_HOST=localhost -e DB_PASSWORD=yourpassword yihua/ccp
   ```

3. **检查网络连接**：
   ```bash
   docker network ls
   docker network inspect bridge
   ```

## 使用问题

### Q: 忘记管理员密码怎么办？

**A:** 可以通过以下方式重置：

1. **使用重置脚本**：
   ```bash
   ./scripts/reset-admin-password.sh
   ```

2. **直接修改数据库**：
   ```sql
   UPDATE users SET password = '$2a$10$...' WHERE username = 'admin';
   ```
   注意：密码需要使用BCrypt加密

3. **联系系统管理员**重置密码

### Q: 客户数据导入失败？

**A:** 检查以下几点：

1. **文件格式是否正确**：
   - 支持CSV、Excel格式
   - 确保字符编码为UTF-8

2. **必填字段是否完整**：
   - 客户姓名（必填）
   - 联系方式（必填）

3. **数据格式是否符合要求**：
   - 邮箱格式：<EMAIL>
   - 电话格式：11位数字

### Q: 消息发送失败，显示"网络错误"？

**A:** 可能的原因和解决方法：

1. **检查网络连接**
2. **确认服务器状态**：
   ```bash
   curl http://localhost:8080/health
   ```
3. **查看系统日志**：
   ```bash
   tail -f logs/yihua-ccp.log
   ```

### Q: 搜索功能不工作或结果不准确？

**A:** 解决步骤：

1. **重建搜索索引**：
   - 进入系统设置 → 搜索管理
   - 点击"重建索引"

2. **检查Elasticsearch服务**：
   ```bash
   curl http://localhost:9200/_cluster/health
   ```

3. **清理缓存**：
   - 系统设置 → 缓存管理 → 清理缓存

## 性能问题

### Q: 系统响应速度慢？

**A:** 性能优化建议：

1. **检查系统资源使用情况**：
   ```bash
   top
   free -h
   df -h
   ```

2. **优化数据库**：
   ```sql
   -- 查看慢查询
   SHOW VARIABLES LIKE 'slow_query_log';
   
   -- 优化表
   OPTIMIZE TABLE customers;
   ```

3. **调整JVM参数**：
   ```bash
   export JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC"
   ```

4. **启用缓存**：
   - 确保Redis服务正常运行
   - 检查缓存配置

### Q: 内存使用过高？

**A:** 内存优化方案：

1. **调整JVM堆内存**：
   ```bash
   # 根据服务器内存调整
   -Xms1g -Xmx2g  # 小型部署
   -Xms2g -Xmx4g  # 中型部署
   -Xms4g -Xmx8g  # 大型部署
   ```

2. **优化数据库连接池**：
   ```yaml
   spring:
     datasource:
       hikari:
         maximum-pool-size: 20
         minimum-idle: 5
   ```

3. **清理临时文件**：
   ```bash
   find /tmp -name "yihua-ccp-*" -delete
   ```

## 集成问题

### Q: API调用返回401错误？

**A:** 认证问题解决：

1. **检查访问令牌是否有效**：
   ```bash
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        http://localhost:8080/api/v1/auth/verify
   ```

2. **重新获取令牌**：
   ```bash
   curl -X POST http://localhost:8080/api/v1/auth/token \
        -H "Content-Type: application/json" \
        -d '{"username":"admin","password":"password"}'
   ```

3. **检查令牌是否过期**

### Q: Webhook接收不到事件通知？

**A:** 排查步骤：

1. **检查Webhook配置**：
   - URL是否可访问
   - 事件类型是否正确

2. **查看Webhook日志**：
   ```bash
   grep "webhook" logs/yihua-ccp.log
   ```

3. **测试网络连通性**：
   ```bash
   curl -X POST https://your-webhook-url.com/test
   ```

### Q: 第三方系统集成失败？

**A:** 集成问题诊断：

1. **检查API文档版本兼容性**
2. **验证请求格式**：
   ```json
   {
     "Content-Type": "application/json",
     "Authorization": "Bearer token"
   }
   ```
3. **查看详细错误信息**
4. **联系技术支持**

## 数据问题

### Q: 数据备份和恢复？

**A:** 备份恢复操作：

1. **数据库备份**：
   ```bash
   mysqldump -u root -p yihua_ccp > backup.sql
   ```

2. **文件备份**：
   ```bash
   tar -czf yihua-ccp-backup.tar.gz /opt/yihua-ccp/data
   ```

3. **数据恢复**：
   ```bash
   mysql -u root -p yihua_ccp < backup.sql
   ```

### Q: 数据迁移到新服务器？

**A:** 迁移步骤：

1. **导出数据**：
   - 数据库数据
   - 上传文件
   - 配置文件

2. **在新服务器安装系统**

3. **导入数据**：
   - 恢复数据库
   - 复制文件
   - 更新配置

4. **验证功能**

## 安全问题

### Q: 如何加强系统安全？

**A:** 安全加固建议：

1. **定期更新密码**：
   - 使用强密码策略
   - 定期更换密码

2. **启用HTTPS**：
   ```nginx
   server {
       listen 443 ssl;
       ssl_certificate /path/to/cert.pem;
       ssl_certificate_key /path/to/key.pem;
   }
   ```

3. **配置防火墙**：
   ```bash
   ufw allow 80
   ufw allow 443
   ufw deny 8080  # 仅内网访问
   ```

4. **启用审计日志**：
   ```yaml
   logging:
     level:
       com.yihua.ccp.audit: INFO
   ```

### Q: 发现安全漏洞怎么办？

**A:** 安全事件处理：

1. **立即评估影响范围**
2. **采取临时防护措施**
3. **联系技术支持团队**
4. **制定修复计划**
5. **更新系统版本**

## 移动端问题

### Q: 移动端无法登录？

**A:** 移动端问题排查：

1. **检查网络连接**
2. **确认服务器地址配置**
3. **清除应用缓存**
4. **重新安装应用**

### Q: 推送通知收不到？

**A:** 推送问题解决：

1. **检查推送服务配置**
2. **确认设备权限设置**
3. **查看推送日志**
4. **测试推送功能**

## 获取帮助

如果以上解决方案无法解决您的问题，可以通过以下方式获取帮助：

### 技术支持

- **邮箱**: <EMAIL>
- **电话**: 400-123-4567
- **在线客服**: 工作日 9:00-18:00

### 社区支持

- **官方论坛**: https://forum.yihua-ccp.com
- **GitHub Issues**: https://github.com/yihua-ccp/yihua-ccp/issues
- **QQ群**: 123456789
- **微信群**: 扫描二维码加入

### 文档资源

- **用户手册**: [使用手册](user-guide.md)
- **API文档**: [API文档](api.md)
- **开发指南**: [开发指南](development.md)
- **视频教程**: https://video.yihua-ccp.com

### 提交问题

提交问题时，请提供以下信息：

1. **系统版本**
2. **操作系统信息**
3. **错误信息截图**
4. **操作步骤描述**
5. **相关日志文件**

这将帮助我们更快地定位和解决问题。
