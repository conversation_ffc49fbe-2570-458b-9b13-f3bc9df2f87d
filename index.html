<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>易华CCP项目文档</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta name="description" content="易华CCP项目的完整文档，包含安装、使用、开发等指南">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">
  <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/docsify@4/lib/themes/vue.css">
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div id="app"></div>
  <script>
    window.$docsify = {
      name: '易华CCP项目文档',
      repo: '',
      loadSidebar: true,
      subMaxLevel: 2,
      auto2top: true,
      coverpage: true,
      search: {
        maxAge: 86400000,
        paths: 'auto',
        placeholder: '搜索文档...',
        noData: '没有找到结果',
        depth: 3
      },
      pagination: {
        previousText: '上一页',
        nextText: '下一页',
        crossChapter: true,
        crossChapterText: true
      }
    }
  </script>
  <!-- Docsify v4 -->
  <script src="//cdn.jsdelivr.net/npm/docsify@4"></script>
  <!-- 搜索插件 -->
  <script src="//cdn.jsdelivr.net/npm/docsify/lib/plugins/search.min.js"></script>
  <!-- 分页插件 -->
  <script src="//cdn.jsdelivr.net/npm/docsify-pagination/dist/docsify-pagination.min.js"></script>
  <!-- 代码复制插件 -->
  <script src="//cdn.jsdelivr.net/npm/docsify-copy-code/dist/docsify-copy-code.min.js"></script>
</body>
</html>
